/**
 * Activity Tracker
 * 
 * This module handles real-time user activity tracking via WebSockets.
 * It monitors user activity and sends heartbeats to the server to maintain online status.
 */

class ActivityTracker {
    constructor() {
        this.socket = null;
        this.connected = false;
        this.heartbeatInterval = null;
        this.reconnectInterval = null;
        this.lastActivity = Date.now();
        this.idleTimeout = 5 * 60 * 1000; // 5 minutes
        this.currentSection = 'chat'; // Default section
        this.statusListeners = [];
        this.debug = false;
    }

    /**
     * Initialize the activity tracker
     * @param {Object} options Configuration options
     */
    init(options = {}) {
        this.debug = options.debug || false;
        this.idleTimeout = options.idleTimeout || this.idleTimeout;
        this.currentSection = options.initialSection || 'chat';

        this.log('Initializing activity tracker');
        
        // Set up activity listeners
        this.setupActivityListeners();
        
        // Connect to the WebSocket server
        this.connect();
        
        // Set up heartbeat interval
        this.startHeartbeat();
        
        // Set up reconnect interval
        this.setupReconnect();
        
        return this;
    }
    
    /**
     * Connect to the WebSocket server
     */
    connect() {
        if (this.socket) {
            try {
                this.socket.close();
            } catch (error) {
                this.error('Error closing existing socket:', error);
            }
            this.socket = null;
        }
        
        try {
            // Create a new socket connection to the activity namespace
            this.socket = io('/activity', {
                transports: ['websocket', 'polling'],  // Try WebSocket first, fall back to polling
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000,
                reconnectionDelayMax: 5000,
                timeout: 20000,
                autoConnect: true
            });
            
            // Set up socket event handlers
            this.setupSocketHandlers();
            
            this.log('Connecting to activity WebSocket');
        } catch (error) {
            this.error('Error connecting to WebSocket:', error);
            this.fallbackToAPI();
        }
    }
    
    /**
     * Set up socket event handlers
     */
    setupSocketHandlers() {
        if (!this.socket) {
            this.error('Socket not initialized');
            return;
        }
        
        // Connection established
        this.socket.on('connect', () => {
            this.connected = true;
            this.log('Connected to activity WebSocket');
            
            // Send initial heartbeat
            this.sendHeartbeat();
            
            // Notify status listeners
            this.notifyStatusListeners('connected');
        });
        
        // Connection lost
        this.socket.on('disconnect', (reason) => {
            this.connected = false;
            this.log('Disconnected from activity WebSocket:', reason);
            
            // Notify status listeners
            this.notifyStatusListeners('disconnected', { reason });
            
            // Try to reconnect after a delay
            setTimeout(() => {
                if (!this.connected) {
                    this.log('Attempting to reconnect after disconnect...');
                    this.connect();
                }
            }, 3000);
        });
        
        // Connection error
        this.socket.on('connect_error', (error) => {
            this.error('WebSocket connection error:', error);
            this.connected = false;
            
            // Notify status listeners
            this.notifyStatusListeners('error', { error: error.toString() });
            
            // Fall back to API if WebSocket fails
            this.fallbackToAPI();
        });
        
        // Heartbeat response
        this.socket.on('heartbeat_response', (data) => {
            this.log('Heartbeat acknowledged:', data);
        });
        
        // User status change
        this.socket.on('user_status_change', (data) => {
            this.log('User status change:', data);
            
            // Notify status listeners
            this.notifyStatusListeners('user_status_change', data);
        });
        
        // Error
        this.socket.on('error', (data) => {
            this.error('WebSocket error:', data);
            this.notifyStatusListeners('socket_error', data);
        });
        
        // Reconnect attempt
        this.socket.io.on('reconnect_attempt', (attempt) => {
            this.log(`Reconnection attempt ${attempt}`);
        });
        
        // Reconnect success
        this.socket.io.on('reconnect', (attempt) => {
            this.log(`Reconnected after ${attempt} attempts`);
            this.connected = true;
            this.notifyStatusListeners('reconnected', { attempts: attempt });
        });
        
        // Reconnect error
        this.socket.io.on('reconnect_error', (error) => {
            this.error('Reconnection error:', error);
            this.notifyStatusListeners('reconnect_error', { error: error.toString() });
        });
        
        // Reconnect failed
        this.socket.io.on('reconnect_failed', () => {
            this.error('Reconnection failed');
            this.notifyStatusListeners('reconnect_failed');
            this.fallbackToAPI();
        });
    }
    
    /**
     * Set up activity listeners to track user activity
     */
    setupActivityListeners() {
        // Track mouse movement
        document.addEventListener('mousemove', () => this.updateActivity());
        
        // Track keyboard input
        document.addEventListener('keydown', () => this.updateActivity());
        
        // Track clicks
        document.addEventListener('click', () => this.updateActivity());
        
        // Track scrolling
        document.addEventListener('scroll', () => this.updateActivity());
        
        // Track page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.updateActivity();
            }
        });
        
        // Track page unload
        window.addEventListener('beforeunload', () => {
            // Try to send a final offline status before the page unloads
            if (this.connected && this.socket) {
                this.socket.emit('disconnect');
            }
        });
        
        this.log('Activity listeners set up');
    }
    
    /**
     * Update the last activity timestamp
     */
    updateActivity() {
        this.lastActivity = Date.now();
    }
    
    /**
     * Start the heartbeat interval
     */
    startHeartbeat() {
        // Clear any existing interval
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        
        // Set up a new interval to send heartbeats every 20 seconds
        this.heartbeatInterval = setInterval(() => {
            // Only send heartbeat if user is active
            if (this.isActive()) {
                this.sendHeartbeat();
            }
        }, 20000); // 20 seconds
        
        this.log('Heartbeat interval started');
    }
    
    /**
     * Set up reconnect interval
     */
    setupReconnect() {
        // Clear any existing interval
        if (this.reconnectInterval) {
            clearInterval(this.reconnectInterval);
        }
        
        // Set up a new interval to check connection every 30 seconds
        this.reconnectInterval = setInterval(() => {
            if (!this.connected && this.isActive()) {
                this.log('Attempting to reconnect...');
                this.connect();
            }
        }, 30000); // 30 seconds
        
        this.log('Reconnect interval started');
    }
    
    /**
     * Send a heartbeat to the server
     */
    sendHeartbeat() {
        if (this.connected && this.socket) {
            this.log('Sending heartbeat for section:', this.currentSection);
            
            try {
                this.socket.emit('heartbeat', {
                    section: this.currentSection,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                this.error('Error sending heartbeat:', error);
                this.fallbackToAPI();
            }
        } else {
            this.fallbackToAPI();
        }
    }
    
    /**
     * Fall back to using the API for activity tracking
     */
    fallbackToAPI() {
        this.log('Falling back to API for activity tracking');
        
        // Only make the API call if the user is active
        if (this.isActive()) {
            fetch('/api/profile/update-activity', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    section: this.currentSection
                })
            })
            .then(response => response.json())
            .then(data => {
                this.log('API activity update response:', data);
            })
            .catch(error => {
                this.error('Error updating activity via API:', error);
            });
        }
    }
    
    /**
     * Check if the user is currently active
     * @returns {boolean} True if the user is active, false otherwise
     */
    isActive() {
        const now = Date.now();
        const timeSinceLastActivity = now - this.lastActivity;
        
        return timeSinceLastActivity < this.idleTimeout;
    }
    
    /**
     * Set the current section
     * @param {string} section The current section
     */
    setSection(section) {
        this.currentSection = section;
        this.log('Section changed to:', section);
        
        // Send an immediate heartbeat with the new section
        this.sendHeartbeat();
    }
    
    /**
     * Add a status listener
     * @param {Function} listener The listener function
     */
    addStatusListener(listener) {
        if (typeof listener === 'function') {
            this.statusListeners.push(listener);
        }
    }
    
    /**
     * Remove a status listener
     * @param {Function} listener The listener function to remove
     */
    removeStatusListener(listener) {
        const index = this.statusListeners.indexOf(listener);
        if (index !== -1) {
            this.statusListeners.splice(index, 1);
        }
    }
    
    /**
     * Notify all status listeners
     * @param {string} event The event name
     * @param {Object} data The event data
     */
    notifyStatusListeners(event, data = null) {
        this.statusListeners.forEach(listener => {
            try {
                listener(event, data);
            } catch (error) {
                this.error('Error in status listener:', error);
            }
        });
    }
    
    /**
     * Log a debug message
     */
    log(...args) {
        if (this.debug) {
            console.log('[ActivityTracker]', ...args);
        }
    }
    
    /**
     * Log an error message
     */
    error(...args) {
        console.error('[ActivityTracker]', ...args);
    }
}

// Create a global instance
window.activityTracker = new ActivityTracker();

// Initialize when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Get the current section from the page if available
    const currentSection = document.body.dataset.section || 'chat';
    
    // Initialize the activity tracker
    window.activityTracker.init({
        debug: false,
        initialSection: currentSection
    });
});