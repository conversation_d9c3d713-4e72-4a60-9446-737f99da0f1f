"""
User Presence WebSocket Handlers
Handles real-time presence updates via WebSocket connections
"""

from flask_socketio import emit, join_room, leave_room, disconnect
from flask_login import current_user
from flask import request
from models.user_presence import UserPresence
from models.friend_relationship import FriendRelationship
from utils.cache import cache
import logging

def init_presence_socketio(socketio):
    """Initialize presence WebSocket event handlers"""
    
    @socketio.on('connect', namespace='/presence')
    def handle_presence_connect():
        """Handle client connection to presence namespace"""
        if not current_user.is_authenticated:
            logging.warning(f"Unauthenticated user attempted to connect to presence namespace: {request.sid}")
            disconnect()
            return False
        
        try:
            # Mark user as online with this session ID
            UserPresence.set_user_online(current_user, request.sid)
            
            # Join user's personal room for receiving status updates
            user_room = f"user_{current_user.id}"
            join_room(user_room)
            
            # Clear any cached status for this user
            cache_key = f'presence_status:{current_user.username}'
            cache.delete(cache_key)
            
            # Notify friends that this user is now online
            notify_friends_status_change(current_user, True)
            
            # Send confirmation to the client
            emit('presence_connected', {
                'success': True,
                'message': 'Connected to presence monitoring',
                'user_id': str(current_user.id),
                'username': current_user.username
            })
            
            logging.info(f"User {current_user.username} connected to presence namespace with session {request.sid}")
            
        except Exception as e:
            logging.error(f"Error handling presence connect for user {current_user.username}: {str(e)}")
            emit('presence_error', {
                'error': 'Failed to establish presence connection'
            })
            disconnect()
    
    @socketio.on('disconnect', namespace='/presence')
    def handle_presence_disconnect():
        """Handle client disconnection from presence namespace"""
        if not current_user.is_authenticated:
            return
        
        try:
            # Mark user as offline
            UserPresence.set_user_offline(current_user, request.sid)
            
            # Clear any cached status for this user
            cache_key = f'presence_status:{current_user.username}'
            cache.delete(cache_key)
            
            # Notify friends that this user is now offline
            notify_friends_status_change(current_user, False)
            
            logging.info(f"User {current_user.username} disconnected from presence namespace")
            
        except Exception as e:
            logging.error(f"Error handling presence disconnect for user {current_user.username}: {str(e)}")
    
    @socketio.on('heartbeat', namespace='/presence')
    def handle_presence_heartbeat():
        """Handle heartbeat to keep connection alive and update last seen"""
        if not current_user.is_authenticated:
            disconnect()
            return
        
        try:
            # Update user's last seen time
            UserPresence.set_user_online(current_user, request.sid)
            
            # Send heartbeat response
            emit('heartbeat_ack', {
                'timestamp': UserPresence.get_user_status(current_user)['last_seen']
            })
            
        except Exception as e:
            logging.error(f"Error handling heartbeat for user {current_user.username}: {str(e)}")
    
    @socketio.on('get_friends_status', namespace='/presence')
    def handle_get_friends_status():
        """Handle request for friends' online status"""
        if not current_user.is_authenticated:
            disconnect()
            return
        
        try:
            # Get user's friends
            friends = FriendRelationship.get_friends(current_user.id)
            friend_ids = [friend.id for friend in friends]
            
            if not friend_ids:
                emit('friends_status_update', {
                    'friends_status': {}
                })
                return
            
            # Get status for all friends
            friends_status = UserPresence.get_friends_status(friend_ids)
            
            # Convert to username-based mapping
            result = {}
            for friend in friends:
                friend_id_str = str(friend.id)
                if friend_id_str in friends_status:
                    result[friend.username] = friends_status[friend_id_str]
                else:
                    result[friend.username] = {
                        'is_online': False,
                        'last_seen': None,
                        'connected_at': None
                    }
            
            emit('friends_status_update', {
                'friends_status': result
            })
            
        except Exception as e:
            logging.error(f"Error getting friends status for user {current_user.username}: {str(e)}")
            emit('presence_error', {
                'error': 'Failed to get friends status'
            })

def notify_friends_status_change(user, is_online):
    """
    Notify all friends of a user's status change
    
    Args:
        user: User object whose status changed
        is_online: Boolean indicating if user is now online or offline
    """
    try:
        # Get user's friends
        friends = FriendRelationship.get_friends(user.id)
        
        # Create status update message
        status_update = {
            'user_id': str(user.id),
            'username': user.username,
            'is_online': is_online,
            'timestamp': UserPresence.get_user_status(user)['last_seen']
        }
        
        # Send update to each friend's room
        socketio = get_socketio_instance()

        if socketio:
            for friend in friends:
                friend_room = f"user_{friend.id}"
                socketio.emit('friend_status_change', status_update, room=friend_room, namespace='/presence')
                logging.debug(f"Sent status update to friend {friend.username} in room {friend_room}")
        else:
            logging.warning("SocketIO instance not available for broadcasting status updates")
        
        logging.info(f"Notified {len(friends)} friends of {user.username}'s status change to {'online' if is_online else 'offline'}")
        
    except Exception as e:
        logging.error(f"Error notifying friends of status change for user {user.username}: {str(e)}")

def broadcast_status_update(user, is_online):
    """
    Broadcast a user's status update to all connected clients who should see it
    
    Args:
        user: User object whose status changed
        is_online: Boolean indicating if user is now online or offline
    """
    try:
        # Clear cached status for this user
        cache_key = f'presence_status:{user.username}'
        cache.delete(cache_key)
        
        # Notify friends
        notify_friends_status_change(user, is_online)
        
    except Exception as e:
        logging.error(f"Error broadcasting status update for user {user.username}: {str(e)}")

# Global function to get the socketio instance for use in other modules
_socketio_instance = None

def set_socketio_instance(socketio):
    """Set the global socketio instance for use in other modules"""
    global _socketio_instance
    _socketio_instance = socketio

def get_socketio_instance():
    """Get the global socketio instance"""
    return _socketio_instance
